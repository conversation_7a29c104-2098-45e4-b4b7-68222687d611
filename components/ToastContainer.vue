<template>
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <Transition
      v-for="toast in toasts"
      :key="toast.id"
      name="toast"
      appear
    >
      <UAlert
        v-if="toast.open"
        :color="getAlertColor(toast.variant)"
        variant="soft"
        :title="toast.title"
        :description="toast.description"
        :close-button="{
          icon: 'i-ph-x',
          color: 'gray',
          variant: 'link',
          padded: false
        }"
        @close="dismiss(toast.id)"
        class="min-w-80 max-w-md shadow-lg"
      />
    </Transition>
  </div>
</template>

<script setup>
const { toasts, dismiss } = useToast()

const getAlertColor = (variant) => {
  const colors = {
    success: 'green',
    error: 'red',
    warning: 'yellow',
    info: 'blue',
    destructive: 'red'
  }
  return colors[variant] || 'blue'
}
</script>

<style scoped>
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
